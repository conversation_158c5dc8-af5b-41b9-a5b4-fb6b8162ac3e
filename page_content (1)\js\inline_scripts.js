
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/www.davidcalik.sk\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.3"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a<n.data.length;a++)if(0!==n.data[a])return!1;return!0}function f(e,t,n,a){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\udde8\ud83c\uddf6","\ud83c\udde8\u200b\ud83c\uddf6")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!a(e,"\ud83e\udedf")}return!1}function g(e,t,n,a){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):s.createElement("canvas"),o=r.getContext("2d",{willReadFrequently:!0}),i=(o.textBaseline="top",o.font="600 32px Arial",{});return e.forEach(function(e){i[e]=t(o,e,n,a)}),i}function t(e){var t=s.createElement("script");t.src=e,t.defer=!0,s.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",i=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){s.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+g.toString()+"("+[JSON.stringify(i),f.toString(),p.toString(),u.toString()].join(",")+"));",a=new Blob([e],{type:"text/javascript"}),r=new Worker(URL.createObjectURL(a),{name:"wpTestEmojiSupports"});return void(r.onmessage=function(e){c(n=e.data),r.terminate(),t(n)})}catch(e){}c(n=g(i,f,p,u))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);



window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}
gtag("set","linker",{"domains":["www.davidcalik.sk"]});
gtag("js", new Date());
gtag("set", "developer_id.dZTNiMT", true);
gtag("config", "GT-P85KT43Q");
 window._googlesitekit = window._googlesitekit || {}; window._googlesitekit.throttledEvents = []; window._googlesitekit.gtagEvent = (name, data) => { var key = JSON.stringify( { name, data } ); if ( !! window._googlesitekit.throttledEvents[ key ] ) { return; } window._googlesitekit.throttledEvents[ key ] = true; setTimeout( () => { delete window._googlesitekit.throttledEvents[ key ]; }, 5 ); gtag( "event", name, { ...data, event_source: "site-kit" } ); };



  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-YJMBVDFEST');



			( function( w, d, s, l, i ) {
				w[l] = w[l] || [];
				w[l].push( {'gtm.start': new Date().getTime(), event: 'gtm.js'} );
				var f = d.getElementsByTagName( s )[0],
					j = d.createElement( s ), dl = l != 'dataLayer' ? '&l=' + l : '';
				j.async = true;
				j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
				f.parentNode.insertBefore( j, f );
			} )( window, document, 'script', 'dataLayer', 'GTM-WD95SFF4' );
			


				(function(c,l,a,r,i,t,y){
			         c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
			         t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
			         y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
			     })(window, document, "clarity", "script", "sj5wts6zwo");

cmplzScriptLoaded();


!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

cmplzScriptLoaded();

var url = window.location.origin + '?ob=open-bridge';
            fbq('set', 'openbridge', '621334987701612', url);
fbq('init', '621334987701612', {}, {
    "agent": "wordpress-6.8.3-4.1.5"
})
cmplzScriptLoaded();


    fbq('track', 'PageView', []);
  
cmplzScriptLoaded();


        jQuery(document).ready(function($) {
            const $slider = $('#nav-slider');

            $slider.slick({
                slidesToShow: 2,
                slidesToScroll: 1,
                dots: false,
                infinite: true,
                autoplay: true,
                pauseOnHover: false,
                autoplaySpeed: 2000,
                speed: 1000,
                arrows: false,
                swipe: true,
                touchMove: true,
                vertical: true,

                responsive: [
                    {
                        breakpoint: 1300,
                        settings: {
                            slidesToShow: 3,
                            vertical: false,
                        }
                    },
                    {
                        breakpoint: 768,
                        settings: {
                            slidesToShow: 1,
                            vertical: false,
                        }
                    },
                ]

            });

            $(document).on('click', '.gallery-starter', function(e) {
                console.log('clicked .gallery-starter'); // Debugging
                e.preventDefault();
                const $fancyboxLinks = $('[data-fancybox="gallery"]');

                if ($fancyboxLinks.length > 0) {
                    $fancyboxLinks.get(0).click(); 
                }
            });

            $('.gallery-button-static').on('click', function(e) {
                e.preventDefault();
                const $fancyboxLinks = $('[data-fancybox="gallery"]');

                if ($fancyboxLinks.length > 0) {
                    $fancyboxLinks.get(0).click(); 
                }
            });

            
            $('.thumb-slide').on('click', function() {
                $(this).find('a[data-fancybox="gallery"]').get(0).click();
            });

            // Fancybox nastavenia
            Fancybox.bind("[data-fancybox='gallery']", {
                Toolbar: {
                    display: ["close", "prev", "next", "zoom", "fullscreen", "thumbs"]
                },
                Thumbs: {
                    autoStart: true
                },
                Carousel: {
                    infinite: true
                },
                Image: {
                    zoom: true,
                    fit: "contain"
                }
            });
        });
    


                window.fluent_form_ff_form_instance_3_1 = {"id":"3","settings":{"layout":{"labelPlacement":"top","asteriskPlacement":"asterisk-right","helpMessagePlacement":"with_label","errorMessagePlacement":"inline","cssClassName":""},"restrictions":{"denyEmptySubmission":{"enabled":false}}},"form_instance":"ff_form_instance_3_1","form_id_selector":"fluentform_3","rules":{"names[first_name]":{"required":{"value":false,"message":"Toto pole je povinn\u00e9.","global_message":"Toto pole je povinn\u00e9.","global":true}},"names[middle_name]":{"required":{"value":false,"message":"Toto pole je povinn\u00e9.","global_message":"Toto pole je povinn\u00e9.","global":true}},"names[last_name]":{"required":{"value":false,"message":"Toto pole je povinn\u00e9.","global_message":"Toto pole je povinn\u00e9.","global":true}},"email":{"required":{"value":true,"message":"Toto pole je povinn\u00e9.","global_message":"Toto pole je povinn\u00e9.","global":true},"email":{"value":true,"message":"Mus\u00edte opu\u017ei\u0165 validn\u00fd email.","global_message":"Mus\u00edte opu\u017ei\u0165 validn\u00fd email.","global":true}},"input_text":{"required":{"value":true,"message":"Toto pole je povinn\u00e9.","global_message":"Toto pole je povinn\u00e9.","global":true}},"description":{"required":{"value":false,"message":"Toto pole je povinn\u00e9.","global_message":"Toto pole je povinn\u00e9.","global":true}}},"debounce_time":300};
                            


{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/generatepress\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}


const filter = document.getElementById("filter");
const mobileFilterBtn = document.getElementById("mobile-filter");
const applyFiltersBtn = document.querySelector(".apply-filters__button");
const closeFilterBtn = document.querySelector("#filter .close");

// Open filter
if (mobileFilterBtn && filter) {
    mobileFilterBtn.addEventListener("click", () => {
        filter.classList.add("open");
    });
}

// Close filter on apply
if (applyFiltersBtn && filter) {
    applyFiltersBtn.addEventListener("click", () => {
        filter.classList.remove("open");
    });
}

// Close filter on close button
if (closeFilterBtn && filter) {
    closeFilterBtn.addEventListener("click", () => {
        filter.classList.remove("open");
    });
}




!function(){"use strict";if("querySelector"in document&&"addEventListener"in window){var e=document.body;e.addEventListener("pointerdown",(function(){e.classList.add("using-mouse")}),{passive:!0}),e.addEventListener("keydown",(function(){e.classList.remove("using-mouse")}),{passive:!0})}}();



        document.addEventListener( 'wpcf7mailsent', function( event ) {
        if( "fb_pxl_code" in event.detail.apiResponse){
            eval(event.detail.apiResponse.fb_pxl_code);
        }
        }, false );
    


var generatepressMenu = {"toggleOpenedSubMenus":true,"openSubMenuLabel":"Otvori\u0165 Sub-Menu","closeSubMenuLabel":"Zatvori\u0165 Sub-Menu"};



window.lazyLoadOptions={elements_selector:"img[data-src],.perfmatters-lazy,.perfmatters-lazy-css-bg",thresholds:"0px 0px",class_loading:"pmloading",class_loaded:"pmloaded",callback_loaded:function(element){if(element.tagName==="IFRAME"){if(element.classList.contains("pmloaded")){if(typeof window.jQuery!="undefined"){if(jQuery.fn.fitVids){jQuery(element).parent().fitVids()}}}}}};window.addEventListener("LazyLoad::Initialized",function(e){var lazyLoadInstance=e.detail.instance;});function perfmattersLazyLoadYouTube(e){var t=document.createElement("iframe"),r="ID?";r+=0===e.dataset.query.length?"":e.dataset.query+"&",r+="autoplay=1",t.setAttribute("src",r.replace("ID",e.dataset.src)),t.setAttribute("frameborder","0"),t.setAttribute("allowfullscreen","1"),t.setAttribute("allow","accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"),e.replaceChild(t,e.firstChild)}



var complianz = {"prefix":"cmplz_","user_banner_id":"1","set_cookies":[],"block_ajax_content":"","banner_version":"25","version":"7.4.2","store_consent":"","do_not_track_enabled":"","consenttype":"optin","region":"eu","geoip":"","dismiss_timeout":"","disable_cookiebanner":"","soft_cookiewall":"","dismiss_on_scroll":"","cookie_expiry":"365","url":"https:\/\/www.davidcalik.sk\/wp-json\/complianz\/v1\/","locale":"lang=sk&locale=sk_SK","set_cookies_on_root":"","cookie_domain":"","current_policy_id":"41","cookie_path":"\/","categories":{"statistics":"\u0161tatistiky","marketing":"marketing"},"tcf_active":"","placeholdertext":"Kliknut\u00edm prijmete s\u00fabory cookie {category} a povol\u00edte tento obsah","css_file":"https:\/\/www.davidcalik.sk\/wp-content\/uploads\/complianz\/css\/banner-{banner_id}-{type}.css?v=25","page_links":{"eu":{"cookie-statement":{"title":"","url":"https:\/\/www.davidcalik.sk\/predaj-nehnutelnosti\/"}}},"tm_categories":"","forceEnableStats":"","preview":"","clean_cookies":"","aria_label":"Kliknut\u00edm prijmete s\u00fabory cookie {category} a povol\u00edte tento obsah"};



var JetEngineSettings = {"ajaxurl":"https:\/\/www.davidcalik.sk\/wp-admin\/admin-ajax.php","ajaxlisting":"https:\/\/www.davidcalik.sk\/ponuka\/5-izbovyrodinnydom-turany\/?nocache=1761339382","restNonce":"5a5dd61366","hoverActionTimeout":"400","post_id":"2643","query_builder":{"custom_ids":[]}};



			jQuery( window ).on( 'jet-engine/frontend/loaded', function() {
				window.JetPlugins.hooks.addFilter(
					'jet-popup.show-popup.data',
					'JetEngine.popupData',
					function( popupData, popup, triggeredBy ) {

						if ( ! triggeredBy ) {
							return popupData;
						}

						if ( ! triggeredBy.data( 'popupIsJetEngine' ) ) {
							return popupData;
						}

						var wrapper = triggeredBy.closest( '.jet-listing-grid__items' );

						if ( wrapper.length && wrapper.data( 'cctSlug' ) ) {
							popupData['cctSlug'] = wrapper.data( 'cctSlug' );
						}

						return popupData;
					}
				);
			} );
		



var fluentFormVars = {"ajaxUrl":"https:\/\/www.davidcalik.sk\/wp-admin\/admin-ajax.php","forms":[],"step_text":"Krok %activeStep% z %totalStep% - %stepTitle%","is_rtl":"","date_i18n":{"previousMonth":"Predch\u00e1dzaj\u00faci mesiac","nextMonth":"Nasleduj\u00faci mesiac","months":{"shorthand":["Jan","Febru\u00e1r","Mar","Apr\u00edl","M\u00e1j","Jun","J\u00fal","Aug","Sep","Okt\u00f3ber","Nov","Dec"],"longhand":["Janu\u00e1r","Febru\u00e1r","Marec","Apr\u00edl","M\u00e1j","J\u00fan","J\u00fal","August","September","Okt\u00f3ber","November","December"]},"weekdays":{"longhand":["Nede\u013ea","Pondelok","Utorok","Streda","\u0160tvrtok","Piatok","Sobota"],"shorthand":["Sun","Mon","\u00dat","St","\u010ct","Pi","Sat"]},"daysInMonth":[31,28,31,30,31,30,31,31,30,31,30,31],"rangeSeparator":" na ","weekAbbreviation":"Wk","scrollTitle":"Pos\u00favajte sa na zv\u00fd\u0161enie","toggleTitle":"Kliknut\u00edm prepnete","amPM":["AM","PM"],"yearAriaLabel":"Rok","firstDayOfWeek":1},"pro_version":"6.1.3","fluentform_version":"6.1.4","force_init":"","stepAnimationDuration":"350","upload_completed_txt":"100 % dokon\u010den\u00e9","upload_start_txt":"0% Dokon\u010den\u00e9","uploading_txt":"Nahr\u00e1vanie","choice_js_vars":{"noResultsText":"Nena\u0161li sa \u017eiadne v\u00fdsledky","loadingText":"Na\u010d\u00edtavanie...","noChoicesText":"\u017diadne mo\u017enosti v\u00fdberu","itemSelectText":"Stla\u010den\u00edm tla\u010didla vyberte","maxItemText":"Je mo\u017en\u00e9 prida\u0165 iba mo\u017enosti %%maxItemCount%%"},"input_mask_vars":{"clearIfNotMatch":false},"nonce":"83f8abc4d4","form_id":"3","step_change_focus":"1","has_cleantalk":"","pro_payment_script_compatible":"1"};


(function(){window.pmDC=1;window.pmDT=15;if(window.pmDT){var e=setTimeout(d,window.pmDT*1e3)}const t=["keydown","mousedown","mousemove","wheel","touchmove","touchstart","touchend"];const n={normal:[],defer:[],async:[]};const o=[];const i=[];var r=false;var a="";window.pmIsClickPending=false;t.forEach(function(e){window.addEventListener(e,d,{passive:true})});if(window.pmDC){window.addEventListener("touchstart",b,{passive:true});window.addEventListener("mousedown",b)}function d(){if(typeof e!=="undefined"){clearTimeout(e)}t.forEach(function(e){window.removeEventListener(e,d,{passive:true})});if(document.readyState==="loading"){document.addEventListener("DOMContentLoaded",s)}else{s()}}async function s(){c();u();f();m();await w(n.normal);await w(n.defer);await w(n.async);await p();document.querySelectorAll("link[data-pmdelayedstyle]").forEach(function(e){e.setAttribute("href",e.getAttribute("data-pmdelayedstyle"))});window.dispatchEvent(new Event("perfmatters-allScriptsLoaded")),E().then(()=>{h()})}function c(){let o={};function e(t,e){function n(e){return o[t].delayedEvents.indexOf(e)>=0?"perfmatters-"+e:e}if(!o[t]){o[t]={originalFunctions:{add:t.addEventListener,remove:t.removeEventListener},delayedEvents:[]};t.addEventListener=function(){arguments[0]=n(arguments[0]);o[t].originalFunctions.add.apply(t,arguments)};t.removeEventListener=function(){arguments[0]=n(arguments[0]);o[t].originalFunctions.remove.apply(t,arguments)}}o[t].delayedEvents.push(e)}function t(t,n){const e=t[n];Object.defineProperty(t,n,{get:!e?function(){}:e,set:function(e){t["perfmatters"+n]=e}})}e(document,"DOMContentLoaded");e(window,"DOMContentLoaded");e(window,"load");e(document,"readystatechange");t(document,"onreadystatechange");t(window,"onload")}function u(){let n=window.jQuery;Object.defineProperty(window,"jQuery",{get(){return n},set(t){if(t&&t.fn&&!o.includes(t)){t.fn.ready=t.fn.init.prototype.ready=function(e){if(r){e.bind(document)(t)}else{document.addEventListener("perfmatters-DOMContentLoaded",function(){e.bind(document)(t)})}};const e=t.fn.on;t.fn.on=t.fn.init.prototype.on=function(){if(this[0]===window){function t(e){e=e.split(" ");e=e.map(function(e){if(e==="load"||e.indexOf("load.")===0){return"perfmatters-jquery-load"}else{return e}});e=e.join(" ");return e}if(typeof arguments[0]=="string"||arguments[0]instanceof String){arguments[0]=t(arguments[0])}else if(typeof arguments[0]=="object"){Object.keys(arguments[0]).forEach(function(e){delete Object.assign(arguments[0],{[t(e)]:arguments[0][e]})[e]})}}return e.apply(this,arguments),this};o.push(t)}n=t}})}function f(){document.querySelectorAll("script[type=pmdelayedscript]").forEach(function(e){if(e.hasAttribute("src")){if(e.hasAttribute("defer")&&e.defer!==false){n.defer.push(e)}else if(e.hasAttribute("async")&&e.async!==false){n.async.push(e)}else{n.normal.push(e)}}else{n.normal.push(e)}})}function m(){var o=document.createDocumentFragment();[...n.normal,...n.defer,...n.async].forEach(function(e){var t=e.getAttribute("src");if(t){var n=document.createElement("link");n.href=t;if(e.getAttribute("data-perfmatters-type")=="module"){n.rel="modulepreload"}else{n.rel="preload";n.as="script"}o.appendChild(n)}});document.head.appendChild(o)}async function w(e){var t=e.shift();if(t){await l(t);return w(e)}return Promise.resolve()}async function l(t){await v();return new Promise(function(e){const n=document.createElement("script");[...t.attributes].forEach(function(e){let t=e.nodeName;if(t!=="type"){if(t==="data-perfmatters-type"){t="type"}n.setAttribute(t,e.nodeValue)}});if(t.hasAttribute("src")){n.addEventListener("load",e);n.addEventListener("error",e)}else{n.text=t.text;e()}t.parentNode.replaceChild(n,t)})}async function p(){r=true;await v();document.dispatchEvent(new Event("perfmatters-DOMContentLoaded"));await v();window.dispatchEvent(new Event("perfmatters-DOMContentLoaded"));await v();document.dispatchEvent(new Event("perfmatters-readystatechange"));await v();if(document.perfmattersonreadystatechange){document.perfmattersonreadystatechange()}await v();window.dispatchEvent(new Event("perfmatters-load"));await v();if(window.perfmattersonload){window.perfmattersonload()}await v();o.forEach(function(e){e(window).trigger("perfmatters-jquery-load")})}async function v(){return new Promise(function(e){requestAnimationFrame(e)})}function h(){window.removeEventListener("touchstart",b,{passive:true});window.removeEventListener("mousedown",b);i.forEach(e=>{if(e.target.outerHTML===a){e.target.dispatchEvent(new MouseEvent("click",{view:e.view,bubbles:true,cancelable:true}))}})}function E(){return new Promise(e=>{window.pmIsClickPending?g=e:e()})}function y(){window.pmIsClickPending=true}function g(){window.pmIsClickPending=false}function L(e){e.target.removeEventListener("click",L);C(e.target,"pm-onclick","onclick");i.push(e),e.preventDefault();e.stopPropagation();e.stopImmediatePropagation();g()}function b(e){if(e.target.tagName!=="HTML"){if(!a){a=e.target.outerHTML}window.addEventListener("touchend",A);window.addEventListener("mouseup",A);window.addEventListener("touchmove",k,{passive:true});window.addEventListener("mousemove",k);e.target.addEventListener("click",L);C(e.target,"onclick","pm-onclick");y()}}function k(e){window.removeEventListener("touchend",A);window.removeEventListener("mouseup",A);window.removeEventListener("touchmove",k,{passive:true});window.removeEventListener("mousemove",k);e.target.removeEventListener("click",L);C(e.target,"pm-onclick","onclick");g()}function A(e){window.removeEventListener("touchend",A);window.removeEventListener("mouseup",A);window.removeEventListener("touchmove",k,{passive:true});window.removeEventListener("mousemove",k)}function C(e,t,n){if(e.hasAttribute&&e.hasAttribute(t)){event.target.setAttribute(n,event.target.getAttribute(t));event.target.removeAttribute(t)}}})();