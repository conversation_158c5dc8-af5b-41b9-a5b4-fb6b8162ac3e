(()=>{function e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function t(t){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?e(Object(i),!0).forEach(function(e){r(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function r(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}jQuery(document).ready(function(){window.fluentFormrecaptchaSuccessCallback=function(e){if(window.innerWidth<768&&/iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var t=jQuery(".g-recaptcha").filter(function(t,r){return grecaptcha.getResponse(t)==e});t.length&&jQuery("html, body").animate({scrollTop:t.first().offset().top-jQuery(window).height()/2},0)}},window.ffValidationError=function(){var e=function(){};return(e.prototype=Object.create(Error.prototype)).constructor=e,e}(),window.ff_helper={numericVal:function(e){if(e.hasClass("ff_numeric")){var t=JSON.parse(e.attr("data-formatter"));return currency(e.val(),t).value}return e.val()||0},formatCurrency:function(e,t){if(e.hasClass("ff_numeric")){var r=JSON.parse(e.attr("data-formatter"));return currency(t,r).format()}return t}},function(e,r){e||(e={}),e.stepAnimationDuration=parseInt(e.stepAnimationDuration);var i={};window.fluentFormApp=function(t){var a=t.attr("data-form_instance");a=a?a.replace(/[^a-zA-Z0-9_-]/g,""):"";var f=window["fluent_form_"+a],s=f&&"object"===n(f)?f:null;if(!s)return console.log("No Fluent form JS vars found!"),!1;if(i[a])return i[a];var c,l,u,d,h,p,m,v,g,_,y,w,b,k,C,x,S,j,T,O,A,F,Q,E,P=s.form_id_selector,D="."+a;return c=o,l={},u=function(){return r("body").find("form"+D)},h=function(e,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"next";t.trigger("update_slider",{goBackToStep:e,animDuration:r,isScrollTop:n,actionType:i})},p=function(e){try{var t=e.find(":input").filter(function(e,t){return"repeater_container"===r(t).attr("data-type")?!r(t).closest(".ff-repeater-container").hasClass("ff_excluded")&&(r(this).closest(".has-conditions").hasClass("ff_excluded")&&r(this).val(""),!0):!r(t).closest(".has-conditions").hasClass("ff_excluded")});k(t);var n=t.serializeArray(),i=n.map(function(e){return e.name});t=t.filter(function(){return!r(this).closest(".ff-el-input--content").find("table").length});var a={};t.each(function(){var t=r(this).attr("name");i.includes(t)||(r(this).is(":checkbox")||r(this).is(":radio"))&&(a[t]||e.find('input[name="'+t+'"]:checked').length||(n.push({name:t,value:""}),a[t]=!0))});var o={data:r.param(r.map(n,function(e){return{name:e.name,value:e.value}})),action:"fluentform_submit",form_id:e.data("form_id")};if(r.each(e.find("[type=file]"),function(e,t){var n={},i=t.name+"[]";n[i]=[],r(t).closest("div").find(".ff-uploaded-list").find(".ff-upload-preview[data-src]").each(function(e,t){n[i][e]=r(this).data("src")}),r.each(n,function(e,t){if(t.length){var n={};n[e]=t,o.data+="&"+r.param(n)}})}),e.find(".ff_uploading").length){var f=r("<div/>",{class:"error text-danger"}),s=r("<span/>",{class:"error-clear",html:"&times;",click:function(e){return r(D+"_errors").html("")}}),c=r("<span/>",{class:"error-text",text:"File upload in progress. Please wait..."});return r(D+"_errors").html(f.append(c,s)).show()}if(e.find(".ff-el-recaptcha.g-recaptcha").length){var u=e.find(".ff-el-recaptcha.g-recaptcha").data("g-recaptcha_widget_id");void 0!==u&&(o.data+="&"+r.param({"g-recaptcha-response":grecaptcha.getResponse(u)}))}if(e.find(".ff-el-hcaptcha.h-captcha").length){var d=e.find(".ff-el-hcaptcha.h-captcha").data("h-captcha_widget_id");void 0!==d&&(o.data+="&"+r.param({"h-captcha-response":hcaptcha.getResponse(d)}))}if(e.find(".ff-el-turnstile.cf-turnstile").length){var h=e.find(".ff-el-turnstile.cf-turnstile").data("cf-turnstile_widget_id");void 0!==h&&(o.data+="&"+r.param({"cf-turnstile-response":turnstile.getResponse(h)}))}r(D+"_success").remove(),r(D+"_errors").html(""),e.find(".error").html(""),e.parent().find(".ff-errors-in-stack").hide(),function(e,t){var r=[],n=l;return e.hasClass("ff_has_v3_recptcha")&&(n.ff_v3_recptcha=function(e,t){var r=jQuery.Deferred(),n=e.data("recptcha_key");return grecaptcha.execute(n,{action:"submit"}).then(function(e){t.data+="&"+jQuery.param({"g-recaptcha-response":e}),r.resolve()}),r.promise()}),jQuery.each(n,function(n,i){r.push(i(e,t))}),jQuery.when.apply(jQuery,r)}(e,o).then(function(){v(e),m(e,o)})}catch(e){if(!(e instanceof ffValidationError))throw e;C(e.messages),w(350)}},m=function(t,n){var i,a,o=(i="t="+Date.now(),a=e.ajaxUrl,a+=(a.split("?")[1]?"&":"?")+i);if(!this.isSending){var f,c=this;this.isSending=!0,r.post(o,n).then(function(n){if(!n||!n.data||!n.data.result)return t.trigger("fluentform_submission_failed",{form:t,response:n}),void C(n);if(f=n,n.data.append_data&&Q(n.data.append_data),n.data.nextAction)t.trigger("fluentform_next_action_"+n.data.nextAction,{form:t,response:n});else{if(t.triggerHandler("fluentform_submission_success",{form:t,config:s,response:n}),jQuery(document.body).trigger("fluentform_submission_success",{form:t,config:s,response:n}),"redirectUrl"in n.data.result)return n.data.result.message&&(r("<div/>",{id:P+"_success",class:"ff-message-success",role:"status","aria-live":"polite"}).html(n.data.result.message).insertAfter(t).focus(),t.find(".ff-el-is-error").removeClass("ff-el-is-error")),void(location.href=n.data.result.redirectUrl);var i=P+"_success",a="#"+i;r(a).length&&r(a).slideUp("fast"),r("<div/>",{id:i,class:"ff-message-success",role:"status","aria-live":"polite"}).html(n.data.result.message).insertAfter(t).focus(),t.find(".ff-el-is-error").removeClass("ff-el-is-error"),"hide_form"==n.data.result.action?(t.hide().addClass("ff_force_hide"),t[0].reset()):(jQuery(document.body).trigger("fluentform_reset",[t,s]),t[0].reset());var o=r(a);o.length&&!b(o[0])&&r("html, body").animate({scrollTop:o.offset().top-(r("#wpadminbar")?32:0)-20},e.stepAnimationDuration)}}).fail(function(r){if(t.trigger("fluentform_submission_failed",{form:t,response:r}),r&&r.responseJSON&&r.responseJSON&&r.responseJSON.errors){if(f=r,r.responseJSON.append_data&&Q(r.responseJSON.append_data),C(r.responseJSON.errors),w(350),t.find(".fluentform-step").length){var n=t.find(".error").not(":empty:first").closest(".fluentform-step");if(n.length){var i=n.index();h(i,e.stepAnimationDuration,!1)}}g(t)}else C(r.responseText)}).always(function(e){var r;if(c.isSending=!1,null===(r=f)||void 0===r||null===(r=r.data)||void 0===r||null===(r=r.result)||void 0===r||!r.hasOwnProperty("redirectUrl")){if(g(t),window.grecaptcha){var n=t.find(".ff-el-recaptcha.g-recaptcha").data("g-recaptcha_widget_id");void 0!==n&&grecaptcha.reset(n)}if(window.hcaptcha){var i=t.find(".ff-el-hcaptcha.h-captcha").data("h-captcha_widget_id");void 0!==i&&hcaptcha.reset(i)}if(window.turnstile){var a=t.find(".ff-el-turnstile.cf-turnstile").data("cf-turnstile_widget_id");void 0!==a&&turnstile.reset(a)}}})}},_=function(){"yes"!=t.attr("data-ff_reinit")&&(r(document).on("submit",D,function(e){e.preventDefault(),window.ff_sumitting_form||(window.ff_sumitting_form=!0,setTimeout(function(){window.ff_sumitting_form=!1},1500),p(r(this)))}),r(document).on("reset",D,function(n){!function(n){r(".ff-step-body",t).length&&h(0,e.stepAnimationDuration,!1),n.find(".ff-el-repeat .ff-t-cell").each(function(){r(this).find("input").not(":first").remove()}),n.find(".ff-el-repeat .ff-el-repeat-buttons-list").find(".ff-el-repeat-buttons").not(":first").remove();var i=n.find("input[type=checkbox],input[type=radio]");i.length&&i.each(function(e,t){(t=r(t)).prop("defaultChecked")?t.closest(".ff-el-form-check").addClass("ff_item_selected"):t.closest(".ff-el-form-check.ff_item_selected").removeClass("ff_item_selected")}),n.find("input[type=file]").closest("div").find(".ff-uploaded-list").html("").end().closest("div").find(".ff-upload-progress").addClass("ff-hidden").find(".ff-el-progress-bar").css("width","0%");var a=n.find('input[type="range"]');a.length&&a.each(function(e,t){(t=r(t)).val(t.data("calc_value")).change()}),r.each(s.conditionals,function(e,t){r.each(t.conditions,function(e,t){y(T(t.field))})})}(r(this))}),r(document).on("keydown",D+' input[type="radio"], '+D+' input[type="checkbox"]',function(e){if("Enter"===e.key)return e.preventDefault(),"radio"===r(this).attr("type")?r(this).prop("checked",!0):"checkbox"===r(this).attr("type")&&r(this).prop("checked",!r(this).prop("checked")),r(this).trigger("change"),e.stopPropagation(),!1}))},y=function(e){var t=e.prop("type");null!=t&&("checkbox"==t||"radio"==t?e.each(function(e,t){var n=r(this);n.prop("checked",n.prop("defaultChecked"))}):t.startsWith("select")?e.find("option").each(function(e,t){var n=r(this);n.prop("selected",n.prop("defaultSelected"))}):e.val(e.prop("defaultValue")),e.trigger("change"))},w=function(e){var n=s.settings.layout.errorMessagePlacement;if(n&&"stackToBottom"!=n){var i=t.find(".ff-el-is-error").first();i.length&&!b(i[0])&&r("html, body").delay(e).animate({scrollTop:i.offset().top-(r("#wpadminbar")?32:0)-20},e)}},b=function(e){if(!e)return!0;var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=r(window).height()&&t.right<=r(window).width()},C=function(e){if(t.parent().find(".ff-errors-in-stack").empty(),e)if("string"!=typeof e){var n=s.settings.layout.errorMessagePlacement;if(!n||"stackToBottom"==n)return x(e),!1;t.find(".error").empty(),t.find(".ff-el-group").removeClass("ff-el-is-error"),r.each(e,function(e,t){"string"==typeof t&&(t=[t]),r.each(t,function(t,r){S(e,r)})})}else x({error:[e]})},x=function(e){var t=u(),n=t.parent().find(".ff-errors-in-stack");e&&(r.isEmptyObject(e)||(r.each(e,function(e,i){"string"==typeof i&&(i=[i]),r.each(i,function(i,a){var o=r("<div/>",{class:"error text-danger"}),f=r("<span/>",{class:"error-clear",html:"&times;"}),s=r("<span/>",{class:"error-text","data-name":T(e).attr("name"),html:a});o.attr("role","alert"),o.append(s,f),r(document.body).trigger("fluentform_error_in_stack",{form:t,element:T(e),message:s}),n.append(o).show()});var a=T(e);if(a){var o=a.attr("name");a.attr("aria-invalid","true");var f=r("[name='"+o+"']").first();f&&f.closest(".ff-el-group").addClass("ff-el-is-error")}}),b(n[0])||r("html, body").animate({scrollTop:n.offset().top-100},350),n.on("click",".error-clear",function(){r(this).closest("div").remove(),n.hide()}).on("click",".error-text",function(){var e=r("[name='".concat(r(this).data("name"),"']")).first();r("html, body").animate({scrollTop:e.offset()&&e.offset().top-100},350,function(t){return e.focus()})})))},S=function(e,n){var i,a;(i=T(e)).length?(i.attr("aria-invalid","true"),(a=r("<div/>",{class:"error text-danger"})).attr("role","alert"),i.closest(".ff-el-group").addClass("ff-el-is-error"),i.closest(".ff-el-input--content").length?(i.closest(".ff-el-input--content").find("div.error").remove(),r(document.body).trigger("fluentform_error_below_element",{form:t,element:i,message:n}),i.closest(".ff-el-input--content").append(a.html(n))):(i.find("div.error").remove(),i.append(a.text(n)))):x([n])},j=function(){t.find(".ff-el-group,.ff_repeater_table, .ff_repeater_container").on("change","input,select,textarea",function(){if(!window.ff_disable_error_clear){r(this).attr("aria-invalid","false");var e=s.settings.layout.errorMessagePlacement;if(e||"stackToBottom"!=e){var t=r(this).closest(".ff-el-group");t.hasClass("ff-el-is-error")&&t.removeClass("ff-el-is-error").find(".error.text-danger").remove()}}})},T=function(e){var t=u(),n=r("[data-name='"+e+"']",t);return(n=n.length?n:r("[name='"+e+"']",t)).length?n:r("[name='"+e+"[]']",t)},O=function(){t.find(".ff-el-recaptcha.g-recaptcha").length&&window.grecaptcha&&"function"==typeof window.grecaptcha.ready&&window.grecaptcha.ready(function(){t.find(".ff-el-recaptcha.g-recaptcha").each(function(){A("g-recaptcha",r(this),grecaptcha.render)})}),t.find(".ff-el-turnstile.cf-turnstile").length&&window.turnstile&&"function"==typeof window.turnstile.ready&&window.turnstile.ready(function(){t.find(".ff-el-turnstile.cf-turnstile").each(function(){A("cf-turnstile",r(this),turnstile.render)})}),t.find(".ff-el-hcaptcha.h-captcha").length&&window.hcaptcha&&t.find(".ff-el-hcaptcha.h-captcha").each(function(){A("h-captcha",r(this),hcaptcha.render)})},A=function(e,t,r){var n=t.data("sitekey"),i=t.attr("id"),a="data-".concat(e,"_widget_id");try{var o=t.attr(a);if("g-recaptcha"===e||"h-captcha"===e){if(o&&t.find("iframe").length>0)return}else if("cf-turnstile"===e){var f=t.find('input[name="cf-turnstile-response"]');if(f.length&&f.val())return;var s=t.attr(a);s&&window.turnstile&&turnstile.remove(s)}var c=i;"cf-turnstile"===e&&(c="#"+i),o=r(c,{sitekey:n}),t.attr(a,o)}catch(t){console.error("Error rendering ".concat(e,":"),t)}},F=function(e,t,r){var n="data-".concat(e,"_widget_id"),i=t.attr(n);if(i)try{return r(i),!0}catch(r){console.error("Error resetting ".concat(e,":"),r),t.removeAttr(n).removeData("".concat(e,"-rendered"))}return!1},Q=function(e){jQuery.each(e,function(e,n){if(n){var i=t.find("input[name="+e+"]");i.length?i.attr("value",n):r("<input>").attr({type:"hidden",name:e,value:n}).appendTo(t)}})},E={initFormHandlers:function(){_(),d(),j(),t.removeClass("ff-form-loading").addClass("ff-form-loaded"),t.on("show_element_error",function(e,t){S(t.element,t.message)})},registerFormSubmissionHandler:_,maybeInlineForm:d=function(){t.hasClass("ff-form-inline")&&t.find("button.ff-btn-submit").css("height","50px")},reinitExtras:function(){t.find(".ff-el-recaptcha.g-recaptcha").length&&window.grecaptcha&&"function"==typeof window.grecaptcha.ready&&window.grecaptcha.ready(function(){t.find(".ff-el-recaptcha.g-recaptcha").each(function(){var e=r(this);F("g-recaptcha",e,grecaptcha.reset)||A("g-recaptcha",e,grecaptcha.render)})}),t.find(".ff-el-turnstile.cf-turnstile").length&&window.turnstile&&"function"==typeof window.turnstile.ready&&window.turnstile.ready(function(){t.find(".ff-el-turnstile.cf-turnstile").each(function(){var e=r(this);F("cf-turnstile",e,turnstile.reset)||A("cf-turnstile",e,turnstile.render)})}),t.find(".ff-el-hcaptcha.h-captcha").length&&window.hcaptcha&&t.find(".ff-el-hcaptcha.h-captcha").each(function(){var e=r(this);F("h-captcha",e,hcaptcha.reset)||A("h-captcha",e,hcaptcha.render)})},initTriggers:function(){t=u(),jQuery(document.body).trigger("fluentform_init",[t,s]),jQuery(document.body).trigger("fluentform_init_"+s.id,[t,s]),t.trigger("fluentform_init_single",[this,s]),t.find("input.ff-el-form-control").on("keypress",function(e){return 13!==e.which}),t.data("is_initialized","yes"),t.find("input.ff-read-only").each(function(){r(this).attr({tabindex:"-1",readonly:"readonly"})}),t.find(".ff-el-tooltip").on("mouseenter",function(e){var n=r(this).data("content"),i=r(".ff-el-pop-content");i.length||(r("<div/>",{class:"ff-el-pop-content"}).appendTo(document.body),i=r(".ff-el-pop-content")),n=n.replace(/<script[^]*?>[^]*?<\/script>/gi,"").replace(/<iframe[^]*?>[^]*?<\/iframe>/gi,"").replace(/<.*?\bon\w+=["'][^"']*["']/gi,"").replace(/javascript:/gi,""),i.html(n);var a=t.innerWidth()-20;i.css("max-width",a);var o=r(this).offset().left,f=i.outerWidth(),s=i.outerHeight(),c=o-f/2+10;c<15&&(c=15),i.css("top",r(this).offset().top-s-5),i.css("left",c)}),t.find(".ff-el-tooltip").on("mouseleave",function(){r(".ff-el-pop-content").remove()}),r(document).on("lity:open",function(){var e;null===(e=window.turnstile)||void 0===e||e.remove(),O()}),t.one("focus",'input, select, textarea, input[type="checkbox"], input[type="radio"]',function(){t.trigger("fluentform_first_interaction")}),t.on("fluentform_first_interaction",function(){O()}),t.on("ff_to_next_page ff_to_prev_page",function(e){O()}),O()},validate:k=function(e){e.length||(e=r("form.frm-fluent-form").find(":input").not(":button").filter(function(e,t){return!r(t).closest(".has-conditions").hasClass("ff_excluded")})),e.each(function(e,t){r(t).closest(".ff-el-group").removeClass("ff-el-is-error").find(".error").remove()}),c().validate(e,s.rules)},showErrorMessages:C,scrollToFirstError:w,settings:s,formSelector:D,sendData:m,addGlobalValidator:function(e,t){l[e]=t},config:s,showFormSubmissionProgress:v=function(e){e.addClass("ff_submitting"),e.find(".ff-btn-submit").addClass("disabled").addClass("ff-working").prop("disabled",!0)},addFieldValidationRule:function(e,t,r){s.rules[e]||(s.rules[e]={}),s.rules[e][t]=r},removeFieldValidationRule:function(e,t){e in s.rules&&t in s.rules[e]&&delete s.rules[e][t]},hideFormSubmissionProgress:g=function(e){e.removeClass("ff_submitting"),e.find(".ff-btn-submit").removeClass("disabled").removeClass("ff-working").attr("disabled",!1),t.parent().find(".ff_msg_temp").remove()}},i[a]=E,E};var a={init:function(){var e=this;setTimeout(function(){e.initMultiSelect()},100),this.initMask(),this.initNumericFormat(),this.initCheckableActive(),this.maybeInitSpamTokenProtection(),this.maybeHandleCleanTalkSubmitTime()},maybeInitSpamTokenProtection:function(){var e=this;jQuery("form.frm-fluent-form").each(function(t,r){var n=jQuery(r),i=n.find(".fluent-form-token-field");if(0!==i.length&&!n.hasClass("ff_tokenizing")&&!n.hasClass("ff_tokenized")){var a=function(){n.hasClass("ff_tokenized")||n.hasClass("ff_tokenizing")||(n.addClass("ff_tokenizing"),e.generateAndSetToken(n,i))};n.one("ff_to_next_page ff_to_prev_page",function(e){a()}),n.on("fluentform_first_interaction",function(){a()})}})},generateAndSetToken:function(t,r){var n,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=t.data("form_id"),o=e.ajaxUrl+"?t="+Date.now(),f=this;jQuery.post(o,{action:"fluentform_generate_protection_token",form_id:a,nonce:null===(n=e)||void 0===n?void 0:n.token_nonce}).done(function(e){e.success&&e.data.token?(r.val(e.data.token),t.addClass("ff_tokenized")):(r.val(null),console.error("Token generation failed for form ID:",a))}).fail(function(e,n,o){console.error("Error generating token for form ID:",a,o),i&&setTimeout(function(){f.generateAndSetToken(t,r,!1)},1e3)}).always(function(){t.removeClass("ff_tokenizing")})},maybeHandleCleanTalkSubmitTime:function(){var e;null!==(e=window.fluentFormVars)&&void 0!==e&&e.has_cleantalk&&jQuery("form.frm-fluent-form").each(function(e,t){var r=jQuery(t).find(".ff_ct_form_load_time");r.length&&r.val(Math.floor(Date.now()/1e3))})},initMultiSelect:function(){r.isFunction(window.Choices)&&r(".ff_has_multi_select").length&&r(".ff_has_multi_select").each(function(e,n){var i=t(t({},{removeItemButton:!0,silent:!0,shouldSort:!1,searchEnabled:!0,searchResultLimit:50}),window.fluentFormVars.choice_js_vars),a=r(n).attr("data-max_selected_options");parseInt(a)&&(i.maxItemCount=parseInt(a),i.maxItemText=function(e){var t=window.fluentFormVars.choice_js_vars.maxItemText;return t=t.replace("%%maxItemCount%%",e)}),i.callbackOnCreateTemplates=function(){r(this.passedElement.element);return{option:function(e){var t=Choices.defaults.templates.option.call(this,e);return e.customProperties&&(t.dataset.calc_value=e.customProperties),t}}},r(n).data("choicesjs",new Choices(n,i))})},initMask:function(){if(null!=jQuery.fn.mask){var e={clearIfNotMatch:window.fluentFormVars.input_mask_vars.clearIfNotMatch,translation:{"*":{pattern:/[0-9a-zA-Z]/},0:{pattern:/\d/},9:{pattern:/\d/,optional:!0},"#":{pattern:/\d/,recursive:!0},A:{pattern:/[a-zA-Z0-9]/},S:{pattern:/[a-zA-Z]/}}};jQuery("input[data-mask]").each(function(t,r){var n=(r=jQuery(r)).attr("data-mask"),i=e;r.attr("data-mask-reverse")&&(i.reverse=!0),r.attr("data-clear-if-not-match")&&(i.clearIfNotMatch=!0),n&&r.mask(n,i)})}},initCheckableActive:function(){r(document).on("change",".ff-el-form-check input[type=radio]",function(){r(this).is(":checked")&&(r(this).closest(".ff-el-input--content").find(".ff-el-form-check").removeClass("ff_item_selected"),r(this).closest(".ff-el-form-check").addClass("ff_item_selected"))}),r(document).on("change",".ff-el-form-check input[type=checkbox]",function(){r(this).is(":checked")?r(this).closest(".ff-el-form-check").addClass("ff_item_selected"):r(this).closest(".ff-el-form-check").removeClass("ff_item_selected")})},initNumericFormat:function(){var e=r("form.frm-fluent-form .ff_numeric");r.each(e,function(e,t){var n=r(t),i=JSON.parse(n.attr("data-formatter"));n.val()&&n.val(window.ff_helper.formatCurrency(n,n.val())),n.on("blur change",function(){var e=currency(r(this).val(),i).format();r(this).val(e)})})}},o=function(){return new function(){this.errors={},this.validate=function(e,t){var n,i,a=this,o=!0;e.each(function(e,f){n=r(f),i=n.prop("name").replace("[]",""),"repeater_item"!==n.data("type")&&"repeater_container"!==n.data("type")||(i=n.attr("data-name"),t[i]=t[n.data("error_index")]),t[i]&&r.each(t[i],function(e,t){e in a&&(a[e](n,t)||(o=!1,i in a.errors||(a.errors[i]={}),a.errors[i][e]=t.message))})}),!o&&this.throwValidationException()},this.throwValidationException=function(){var e=new ffValidationError("Validation Error!");throw e.messages=this.errors,e},this.required=function(e,t){if(!t.value)return!0;var n=e.prop("type");if("checkbox"==n||"radio"==n)return e.parents(".ff-el-group").attr("data-name")&&!t.per_row?e.parents(".ff-el-group").find("input:checked").length:r('[name="'+e.prop("name")+'"]:checked').length;if(n.startsWith("select")){var i=e.find(":selected");return!(!i.length||!i.val().length)}return"file"==n?e.closest("div").find(".ff-uploaded-list").find(".ff-upload-preview[data-src]").length:"false"==e.attr("is-changed")?"":String(r.trim(e.val())).length},this.url=function(e,t){var r=e.val();if(!t.value||!r.length)return!0;return/^(ftp|http|https):\/\/[^ "]+$/.test(r)},this.email=function(e,t){var r=e.val();if(!t.value||!r.length)return!0;return/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(r.toLowerCase())},this.numeric=function(e,t){var n=window.ff_helper.numericVal(e);return n=n.toString(),!t.value||!n||r.isNumeric(n)},this.min=function(e,t){if(!e.val())return!0;var r=window.ff_helper.numericVal(e);return r=r.toString(),!t.value||!r.length||(this.numeric(e,t)?Number(r)>=Number(t.value):void 0)},this.max=function(e,t){if(!e.val())return!0;var r=window.ff_helper.numericVal(e);return r=r.toString(),!t.value||!r.length||(this.numeric(e,t)?Number(r)<=Number(t.value):void 0)},this.digits=function(e,t){if(!e.val())return!0;var r=window.ff_helper.numericVal(e);return r=r.toString(),!t.value||!r.length||this.numeric(e,t)&&r.length==t.value},this.max_file_size=function(){return!0},this.max_file_count=function(){return!0},this.allowed_file_types=function(){return!0},this.allowed_image_types=function(){return!0},this.force_failed=function(){return!1},this.valid_phone_number=function(e,t){if(!e.val())return!0;if(e&&e[0]){var r;if(!(r=void 0!==window.intlTelInputGlobals?window.intlTelInputGlobals.getInstance(e[0]):e.data("iti")))return!0;if(e.hasClass("ff_el_with_extended_validation"))return!!r.isValidNumber()&&(e.val(r.getNumber()),!0);var n=r.getSelectedCountryData(),i=e.val();return!e.attr("data-original_val")&&i&&n&&n.dialCode&&(e.val("+"+n.dialCode+i),e.attr("data-original_val",i)),!0}}}},f=r("form.frm-fluent-form");function s(e){var t=fluentFormApp(e);if(t)t.initFormHandlers(),t.initTriggers();else var r=0,n=setInterval(function(){(t=fluentFormApp(e))&&(clearInterval(n),t.initFormHandlers(),t.initTriggers()),++r>10&&(clearInterval(n),console.log("Form could not be loaded"))},1e3)}function c(){r(".ff_has_multi_select").each(function(){var e=r(this).data("choicesjs");e&&e.passedElement&&e.passedElement.element.addEventListener("showDropdown",function(){var e=this.closest(".choices");if(e){var t=e.querySelector(".choices__list--dropdown");if(t){t.style.maxHeight="300px",t.style.overflowY="auto";var r=t.querySelector('.choices__list[role="listbox"]')||t.querySelector(".choices__list:not(.choices__list--dropdown)");r&&(r.style.maxHeight="280px",r.style.overflowY="auto",r.style.webkitOverflowScrolling="touch",r.style.touchAction="pan-y")}}},{passive:!0})})}r.each(f,function(e,t){s(r(t))}),r(document).on("ff_reinit",function(e,t){var n=r(t),i=fluentFormApp(n);if(!i)return!1;i.reinitExtras(),s(n),a.init(),n.attr("data-ff_reinit","yes")}),a.init(),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){setTimeout(c,100)}):setTimeout(c,100)}(window.fluentFormVars,jQuery),jQuery(".fluentform").on("submit",".ff-form-loading",function(e){e.preventDefault(),jQuery(this).parent().find(".ff_msg_temp").remove(),jQuery("<div/>",{class:"error text-danger ff_msg_temp"}).html("Javascript handler could not be loaded. Form submission has been failed. Reload the page and try again").insertAfter(jQuery(this))})})})();