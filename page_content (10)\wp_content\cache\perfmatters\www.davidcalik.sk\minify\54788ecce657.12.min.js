const footerLists=document.querySelectorAll('.footer-list');if(footerLists)footerLists.forEach((list,index)=>{index===0?list.classList.add('open'):null;list.querySelector('.footer-list__heading').addEventListener('click',()=>{list.classList.toggle('open')})})
document.addEventListener('DOMContentLoaded',()=>{const container=document.querySelector('.ponuka-gallery__inner__cols');if(!container)return;const button=container.querySelector('.gallery-button-static');if(!button)return;const match=button.textContent.match(/\((\d+)\)/);if(!match)return;const count=parseInt(match[1],10);if(count<=1){container.classList.add('no-gallery')}})