img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }



	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}



/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}



:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--color--color-brand-main: var(--color-brand-main);--wp--preset--color--color-brand-blue: var(--color-brand-blue);--wp--preset--color--color-brand-green: var(--color-brand-green);--wp--preset--color--color-brand-light: var(--color-brand-light);--wp--preset--color--color-neutral-8: var(--color-neutral-8);--wp--preset--color--color-neutral-7: var(--color-neutral-7);--wp--preset--color--color-neutral-6: var(--color-neutral-6);--wp--preset--color--color-neutral-5: var(--color-neutral-5);--wp--preset--color--color-neutral-4: var(--color-neutral-4);--wp--preset--color--color-neutral-3: var(--color-neutral-3);--wp--preset--color--color-neutral-2: var(--color-neutral-2);--wp--preset--color--color-neutral-1: var(--color-neutral-1);--wp--preset--color--color-black: var(--color-black);--wp--preset--color--color-white: var(--color-white);--wp--preset--color--color-brand-orange: var(--color-brand-orange);--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}



body{background-color:var(--color-brand-blue);color:var(--color-brand-light);}a{color:var(--color-brand-main);}a{text-decoration:underline;}.entry-title a, .site-branding a, a.button, .wp-block-button__link, .main-navigation a{text-decoration:none;}a:hover, a:focus, a:active{color:#ec7551;}.grid-container{max-width:1500px;}.wp-block-group__inner-container{max-width:1500px;margin-left:auto;margin-right:auto;}.site-header .header-image{width:160px;}:root{--color-brand-main:#f9dc05;--color-brand-blue:#1B2E48;--color-brand-green:#C3DF94;--color-brand-light:#FAF4DF;--color-neutral-8:#141416;--color-neutral-7:#23262F;--color-neutral-6:#353945;--color-neutral-5:#777E90;--color-neutral-4:#B1B5C3;--color-neutral-3:#E6E8EC;--color-neutral-2:#F4F5F6;--color-neutral-1:#FCFCFD;--color-black:#000000;--color-white:#ffffff;--color-brand-orange:#E3562B;}:root .has-color-brand-main-color{color:var(--color-brand-main);}:root .has-color-brand-main-background-color{background-color:var(--color-brand-main);}:root .has-color-brand-blue-color{color:var(--color-brand-blue);}:root .has-color-brand-blue-background-color{background-color:var(--color-brand-blue);}:root .has-color-brand-green-color{color:var(--color-brand-green);}:root .has-color-brand-green-background-color{background-color:var(--color-brand-green);}:root .has-color-brand-light-color{color:var(--color-brand-light);}:root .has-color-brand-light-background-color{background-color:var(--color-brand-light);}:root .has-color-neutral-8-color{color:var(--color-neutral-8);}:root .has-color-neutral-8-background-color{background-color:var(--color-neutral-8);}:root .has-color-neutral-7-color{color:var(--color-neutral-7);}:root .has-color-neutral-7-background-color{background-color:var(--color-neutral-7);}:root .has-color-neutral-6-color{color:var(--color-neutral-6);}:root .has-color-neutral-6-background-color{background-color:var(--color-neutral-6);}:root .has-color-neutral-5-color{color:var(--color-neutral-5);}:root .has-color-neutral-5-background-color{background-color:var(--color-neutral-5);}:root .has-color-neutral-4-color{color:var(--color-neutral-4);}:root .has-color-neutral-4-background-color{background-color:var(--color-neutral-4);}:root .has-color-neutral-3-color{color:var(--color-neutral-3);}:root .has-color-neutral-3-background-color{background-color:var(--color-neutral-3);}:root .has-color-neutral-2-color{color:var(--color-neutral-2);}:root .has-color-neutral-2-background-color{background-color:var(--color-neutral-2);}:root .has-color-neutral-1-color{color:var(--color-neutral-1);}:root .has-color-neutral-1-background-color{background-color:var(--color-neutral-1);}:root .has-color-black-color{color:var(--color-black);}:root .has-color-black-background-color{background-color:var(--color-black);}:root .has-color-white-color{color:var(--color-white);}:root .has-color-white-background-color{background-color:var(--color-white);}:root .has-color-brand-orange-color{color:var(--color-brand-orange);}:root .has-color-brand-orange-background-color{background-color:var(--color-brand-orange);}.top-bar{background-color:var(--color-brand-blue);color:var(--color-brand-light);}.top-bar a{color:var(--color-brand-main);}.top-bar a:hover{color:#ec7551;}.site-header{background-color:var(--color-brand-blue);color:var(--color-brand-light);}.site-header a{color:var(--color-brand-main);}.site-header a:hover{color:#ec7551;}.main-title a,.main-title a:hover{color:var(--color-brand-light);}.site-description{color:var(--color-brand-light);}.main-navigation,.main-navigation ul ul{background-color:rgba(0,0,0,0);}.main-navigation .main-nav ul li a, .main-navigation .menu-toggle, .main-navigation .menu-bar-items{color:var(--color-brand-light);}.main-navigation .main-nav ul li:not([class*="current-menu-"]):hover > a, .main-navigation .main-nav ul li:not([class*="current-menu-"]):focus > a, .main-navigation .main-nav ul li.sfHover:not([class*="current-menu-"]) > a, .main-navigation .menu-bar-item:hover > a, .main-navigation .menu-bar-item.sfHover > a{color:#f3e7b5;}button.menu-toggle:hover,button.menu-toggle:focus{color:var(--color-brand-light);}.main-navigation .main-nav ul li[class*="current-menu-"] > a{color:var(--color-brand-main);}.navigation-search input[type="search"],.navigation-search input[type="search"]:active, .navigation-search input[type="search"]:focus, .main-navigation .main-nav ul li.search-item.active > a, .main-navigation .menu-bar-items .search-item.active > a{color:#f3e7b5;}.main-navigation ul ul{background-color:rgba(0,0,0,0);}.separate-containers .inside-article, .separate-containers .comments-area, .separate-containers .page-header, .one-container .container, .separate-containers .paging-navigation, .inside-page-header{background-color:var(--base-3);}.entry-title a{color:var(--contrast);}.entry-title a:hover{color:var(--contrast-2);}.entry-meta{color:var(--contrast-2);}.sidebar .widget{background-color:var(--base-3);}.footer-widgets{background-color:var(--base-3);}.site-info{background-color:var(--base-3);}input[type="text"],input[type="email"],input[type="url"],input[type="password"],input[type="search"],input[type="tel"],input[type="number"],textarea,select{color:var(--contrast);background-color:var(--base-2);border-color:var(--base);}input[type="text"]:focus,input[type="email"]:focus,input[type="url"]:focus,input[type="password"]:focus,input[type="search"]:focus,input[type="tel"]:focus,input[type="number"]:focus,textarea:focus,select:focus{color:var(--contrast);background-color:var(--base-2);border-color:var(--contrast-3);}button,html input[type="button"],input[type="reset"],input[type="submit"],a.button,a.wp-block-button__link:not(.has-background){color:var(--color-brand-light);background-color:var(--color-brand-main);}button:hover,html input[type="button"]:hover,input[type="reset"]:hover,input[type="submit"]:hover,a.button:hover,button:focus,html input[type="button"]:focus,input[type="reset"]:focus,input[type="submit"]:focus,a.button:focus,a.wp-block-button__link:not(.has-background):active,a.wp-block-button__link:not(.has-background):focus,a.wp-block-button__link:not(.has-background):hover{color:var(--color-brand-light);background-color:var(--color-brand-main);}a.generate-back-to-top{background-color:rgba( 0,0,0,0.4 );color:#ffffff;}a.generate-back-to-top:hover,a.generate-back-to-top:focus{background-color:rgba( 0,0,0,0.6 );color:#ffffff;}:root{--gp-search-modal-bg-color:var(--base-3);--gp-search-modal-text-color:var(--contrast);--gp-search-modal-overlay-bg-color:rgba(0,0,0,0.2);}@media (max-width:768px){.main-navigation .menu-bar-item:hover > a, .main-navigation .menu-bar-item.sfHover > a{background:none;color:var(--color-brand-light);}}.nav-below-header .main-navigation .inside-navigation.grid-container, .nav-above-header .main-navigation .inside-navigation.grid-container{padding:0px 20px 0px 20px;}.site-main .wp-block-group__inner-container{padding:40px;}.separate-containers .paging-navigation{padding-top:20px;padding-bottom:20px;}.entry-content .alignwide, body:not(.no-sidebar) .entry-content .alignfull{margin-left:-40px;width:calc(100% + 80px);max-width:calc(100% + 80px);}.rtl .menu-item-has-children .dropdown-menu-toggle{padding-left:20px;}.rtl .main-navigation .main-nav ul li.menu-item-has-children > a{padding-right:20px;}@media (max-width:768px){.separate-containers .inside-article, .separate-containers .comments-area, .separate-containers .page-header, .separate-containers .paging-navigation, .one-container .site-content, .inside-page-header{padding:30px;}.site-main .wp-block-group__inner-container{padding:30px;}.inside-top-bar{padding-right:30px;padding-left:30px;}.inside-header{padding-right:30px;padding-left:30px;}.widget-area .widget{padding-top:30px;padding-right:30px;padding-bottom:30px;padding-left:30px;}.footer-widgets-container{padding-top:30px;padding-right:30px;padding-bottom:30px;padding-left:30px;}.inside-site-info{padding-right:30px;padding-left:30px;}.entry-content .alignwide, body:not(.no-sidebar) .entry-content .alignfull{margin-left:-30px;width:calc(100% + 60px);max-width:calc(100% + 60px);}.one-container .site-main .paging-navigation{margin-bottom:20px;}}/* End cached CSS */.is-right-sidebar{width:30%;}.is-left-sidebar{width:30%;}.site-content .content-area{width:100%;}@media (max-width:768px){.main-navigation .menu-toggle,.sidebar-nav-mobile:not(#sticky-placeholder){display:block;}.main-navigation ul,.gen-sidebar-nav,.main-navigation:not(.slideout-navigation):not(.toggled) .main-nav > ul,.has-inline-mobile-toggle #site-navigation .inside-navigation > *:not(.navigation-search):not(.main-nav){display:none;}.nav-align-right .inside-navigation,.nav-align-center .inside-navigation{justify-content:space-between;}.has-inline-mobile-toggle .mobile-menu-control-wrapper{display:flex;flex-wrap:wrap;}.has-inline-mobile-toggle .inside-header{flex-direction:row;text-align:left;flex-wrap:wrap;}.has-inline-mobile-toggle .header-widget,.has-inline-mobile-toggle #site-navigation{flex-basis:100%;}.nav-float-left .has-inline-mobile-toggle #site-navigation{order:10;}}
.dynamic-author-image-rounded{border-radius:100%;}.dynamic-featured-image, .dynamic-author-image{vertical-align:middle;}.one-container.blog .dynamic-content-template:not(:last-child), .one-container.archive .dynamic-content-template:not(:last-child){padding-bottom:0px;}.dynamic-entry-excerpt > p:last-child{margin-bottom:0px;}



.gb-container-1c00f9d1{display:flex;flex-direction:column;row-gap:100px;margin-right:auto;margin-left:auto;}.gb-container-16b1581d{display:flex;flex-direction:row;flex-wrap:wrap;column-gap:80px;}.gb-container-3ffb881f{display:flex;align-items:center;justify-content:space-between;}.gb-container-b5be40fb{display:flex;column-gap:30px;}.gb-image-a848d507{width:300px;height:auto;vertical-align:middle;}h6.gb-headline-64a0715b{font-size:20px;}h6.gb-headline-d5b898af{font-size:20px;}h6.gb-headline-1e92b246{font-size:20px;}h6.gb-headline-3186df22{font-size:20px;}h6.gb-headline-187f99a9{font-size:20px;}@media (max-width: 1024px) {.gb-container-1c00f9d1{row-gap:80px;}.gb-container-b5be40fb{flex-direction:column;row-gap:5px;}}@media (max-width: 767px) {.gb-container-1c00f9d1{row-gap:50px;}.gb-container-b5be40fb{flex-direction:column;row-gap:5px;}}:root{--gb-container-width:1500px;}.gb-container .wp-block-image img{vertical-align:middle;}.gb-grid-wrapper .wp-block-image{margin-bottom:0;}.gb-highlight{background:none;}.gb-shape{line-height:0;}.gb-element-93a67251{align-items:center;column-gap:24px;display:flex;row-gap:24px}.gb-element-d40bebdf{margin-left:12px}.gb-element-3409bbce{overflow-x:hidden;overflow-y:hidden}.gb-element-9947ca85{margin-left:auto;margin-right:auto;max-width:1500px;padding-bottom:0px;padding-top:16px}@media (max-width:767px){.gb-element-9947ca85{padding-top:24px}}.gb-element-1b32aad9{column-gap:20px;display:flex;row-gap:20px}@media (max-width:767px){.gb-element-1b32aad9{padding-bottom:12px}}.gb-element-6228135a{max-width:70%;width:70%}.gb-element-99c9e829{align-items:center;column-gap:10px;display:flex;flex-direction:row;flex-wrap:wrap;justify-content:center;row-gap:10px}.gb-element-b3743ace{max-width:30%;width:30%}.gb-element-c17bff4d{margin-left:auto;margin-right:auto;max-width:1500px;padding-bottom:30px;padding-top:0px}.gb-element-0d87e360{column-gap:20px;display:flex;row-gap:20px}.gb-element-45eea116{max-width:70%;width:70%}.gb-element-542f883e{align-items:center;column-gap:12px;display:grid;grid-template-columns:repeat(4,minmax(0,1fr));margin-bottom:-112px;position:relative;row-gap:16px;top:0px;z-index:3;border-radius:8px;padding:12px}.gb-element-1a7c61d9{align-items:center;display:flex;justify-content:center;border:1px solid var(--color-neutral-3);border-radius:8px;padding:32px}.gb-element-ac43c0d8{align-items:center;display:flex;justify-content:center;border:1px solid var(--color-neutral-3);border-radius:8px;padding:32px}.gb-element-d267c0e5{align-items:center;display:flex;justify-content:center;border:1px solid var(--color-neutral-3);border-radius:8px;padding:32px}.gb-element-751fb382{align-items:center;display:flex;justify-content:center;border:1px solid var(--color-neutral-3);border-radius:8px;padding:32px}.gb-element-4acfe234{margin-left:auto;margin-right:auto;margin-top:100px;max-width:750px}@media (max-width:767px){.gb-element-4acfe234{margin-top:50px}}.gb-element-9c130c31{max-width:30%;padding-top:100px;width:30%}.gb-element-e0586fa5{background-color:var(--color-white);display:flex;flex-direction:column;position:sticky;row-gap:24px;top:120px;border-radius:8px;padding:24px}.gb-element-4755a410{align-items:center;column-gap:12px;display:flex;row-gap:12px}.gb-element-11e1e7c3{align-items:flex-start;display:flex;flex-direction:column;justify-content:center}.gb-element-bf1a6093{display:flex;flex-direction:column;justify-content:flex-start;margin-left:auto;margin-right:auto;max-width:1600px;padding-bottom:80px;padding-top:120px;row-gap:80px}.gb-element-08def86d{margin-left:auto;margin-right:auto;max-width:var(--gb-container-width);padding-bottom:120px}.gb-element-9cc5546a{align-items:center;column-gap:24px;display:grid;flex-direction:row;grid-template-columns:repeat(4,minmax(0,1fr));row-gap:24px}@media (max-width:1024px){.gb-element-9cc5546a{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (max-width:767px){.gb-element-9cc5546a{grid-template-columns:1fr;row-gap:16px}}.gb-element-8993a045{align-items:center;column-gap:24px;display:flex;justify-content:center;row-gap:16px;border:1px solid var(--color-neutral-5);border-radius:5px;padding:36px 24px}.gb-element-8993a045:hover{background-color:rgba(242,244,245,0.02)}.gb-element-8993a045 a{text-decoration:none;border-top-color:var(--color-neutral-5);border-right-color:var(--color-neutral-5);border-bottom-color:var(--color-neutral-5);border-left-color:var(--color-neutral-5)}@media (max-width:767px){.gb-element-8993a045{padding:20px}}.gb-element-6eee7b3d{display:flex;flex-direction:column;row-gap:0px}.gb-element-69a34d6e{align-items:center;column-gap:24px;display:flex;justify-content:center;row-gap:16px;border:1px solid var(--color-neutral-4);border-radius:5px;padding:36px 24px}.gb-element-69a34d6e:hover{background-color:rgba(242,244,245,0.02)}.gb-element-69a34d6e a{text-decoration:none;border-top-color:var(--color-neutral-5);border-right-color:var(--color-neutral-5);border-bottom-color:var(--color-neutral-5);border-left-color:var(--color-neutral-5)}@media (max-width:767px){.gb-element-69a34d6e{padding:20px}}.gb-element-07f84bad{display:flex;flex-direction:column;row-gap:0px}.gb-element-2fa62a60{align-items:center;column-gap:24px;display:flex;justify-content:center;row-gap:16px;border:1px solid var(--color-neutral-4);border-radius:5px;padding:36px 24px}.gb-element-2fa62a60:hover{background-color:rgba(242,244,245,0.02)}.gb-element-2fa62a60 a{text-decoration:none;border-top-color:var(--color-neutral-5);border-right-color:var(--color-neutral-5);border-bottom-color:var(--color-neutral-5);border-left-color:var(--color-neutral-5)}@media (max-width:767px){.gb-element-2fa62a60{padding:20px}}.gb-element-aeebf857{display:flex;flex-direction:column;row-gap:0px}.gb-element-dea8d9b8{align-items:center;column-gap:24px;display:flex;justify-content:center;row-gap:16px;border:1px solid var(--color-neutral-4);border-radius:5px;padding:36px 24px}.gb-element-dea8d9b8:hover{background-color:rgba(242,244,245,0.02)}.gb-element-dea8d9b8 a{text-decoration:none;border-top-color:var(--color-neutral-5);border-right-color:var(--color-neutral-5);border-bottom-color:var(--color-neutral-5);border-left-color:var(--color-neutral-5)}@media (max-width:767px){.gb-element-dea8d9b8{padding:20px}}.gb-element-91263394{display:flex;flex-direction:column;row-gap:0px}.gb-element-02614999{padding:24px}@media (max-width:767px){.gb-element-02614999{padding-left:12px;padding-right:12px}}.gb-element-ae87510b{border-radius:12px}.gb-element-2d357751{align-items:center;background-color:var(--color-brand-main);bottom:0px;box-shadow:0px 0px 20px 1px rgba(21,21,23,0.33);column-gap:6px;display:flex;height:50px;justify-content:center;left:0px;margin-left:auto;margin-right:0px;position:static;right:0px;row-gap:8px;text-decoration:none;top:0px;width:fit-content;z-index:10;border-radius:200px;padding:8px 15px 8px 8px}.gb-element-2d357751 a{color:var(--color-brand-main);text-decoration:none;display:flex;align-items:center;justify-content:flex-start}@media (max-width:767px){.gb-element-2d357751{bottom:0px;column-gap:5px;height:45px;right:0px;row-gap:6px;border-radius:200px;padding:5px 16px 5px 5px}}.gb-element-8bbf57e0{align-items:center;background-color:var(--color-brand-blue);display:flex;justify-content:center;border-radius:200%;padding:8px}.gb-media-7447c7c5{height:auto;max-width:100%;object-fit:cover;width:auto}.gb-media-49bbe0f1{height:auto;max-width:100%;object-fit:cover;width:auto}.gb-media-cd7d9f5b{max-width:100%;object-fit:cover}.gb-media-d9b84751{height:80px;max-width:100%;object-fit:cover;width:80px;border-radius:8px}.gb-media-b5c03388{height:auto;max-width:100%;object-fit:cover;width:auto}.gb-media-f6a6c15c{height:auto;max-width:100%;object-fit:cover;width:auto}.gb-media-6cd53c05{height:auto;max-width:100%;object-fit:cover;width:auto}.gb-media-4987c9b8{height:50px;max-width:100%;object-fit:cover;width:50px}.gb-media-3787734d{height:50px;max-width:100%;object-fit:cover;width:50px}.gb-media-54d50a43{height:50px;max-width:100%;object-fit:cover;width:50px}.gb-media-89bf2de6{height:50px;max-width:100%;object-fit:cover;width:50px}.gb-media-bf700bf5{filter:invert(100%);height:20px;max-width:100%;object-fit:cover;width:20px;border-radius:0%;padding:0px}@media (max-width:767px){.gb-media-bf700bf5{height:20px;width:20px}}.gb-text-e0d095bc{color:var(--color-brand-blue);font-size:18px;margin-bottom:-1px}.gb-text-661f2279{color:var(--color-neutral-4);font-size:14px;margin-top:-1px}.gb-text-43c8018a{color:var(--color-brand-blue);font-size:14px;font-weight:bold}.gb-text-43c8018a a{color:var(--color-brand-blue)}.gb-text-3ad774c6{color:var(--color-neutral-7);font-size:16px;margin-bottom:-4px}.gb-text-d8caa6de{color:var(--color-neutral-4);font-size:15px;margin-bottom:-6px;text-decoration:none}.gb-text-d8caa6de a{text-decoration:none}.gb-text-63cb7e58{color:var(--color-white);font-size:24px;margin-bottom:-4px;margin-top:-6;text-decoration:none}.gb-text-6f0227f0{color:var(--color-neutral-4);font-size:15px;margin-bottom:-6px;text-decoration:none}.gb-text-6f0227f0 a{text-decoration:none}.gb-text-59a55a2c{color:var(--color-white);font-size:24px;margin-bottom:-4px;margin-top:-6;text-decoration:none}.gb-text-ff616a7d{color:var(--color-neutral-4);font-size:15px;margin-bottom:-6px;text-decoration:none}.gb-text-ff616a7d a{text-decoration:none}.gb-text-860d06e7{color:var(--color-white);font-size:24px;margin-bottom:-4px;margin-top:-6;text-decoration:none}.gb-text-9b419555{color:var(--color-neutral-4);font-size:15px;margin-bottom:-6px;text-decoration:none}.gb-text-9b419555 a{text-decoration:none}.gb-text-a34ec4ba{color:var(--color-white);font-size:24px;margin-bottom:-4px;margin-top:-6;text-decoration:none}.gb-text-8d5c3f3b{color:var(--color-neutral-6);display:block;font-size:12px;line-height:1.35em;text-decoration:none}@media (max-width:767px){.gb-text-8d5c3f3b{color:var(--color-neutral-6);line-height:1.2em}}



/*-----------------------------------------------------------*/
/* LAYOUTS                                                   */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* COLORS                                                    */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* FONTS                                                     */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* OTHER                                                    */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* LAYOUTS                                                   */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* COLORS                                                    */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* FONTS                                                     */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* OTHER                                                    */
/*-----------------------------------------------------------*/
/*-----------------------------------------------------------*/
/* TYPOGRAPHY                                                */
/*-----------------------------------------------------------*/
h1 {
  font-size: 62px;
  font-weight: normal;
  line-height: 1.3em;
  letter-spacing: -0.02em;
}
h2 {
  font-size: 40px;
  font-weight: normal;
  line-height: 1.3em;
  letter-spacing: -0.02em;
}
h3 {
  font-size: 32px;
  font-weight: normal;
  line-height: 1.3em;
  letter-spacing: -0.02em;
}
h4 {
  font-size: 28px;
  font-weight: normal;
  line-height: 1.3em;
  letter-spacing: -0.02em;
}
h5 {
  font-size: 24px;
  font-weight: normal;
  line-height: 1.3em;
  letter-spacing: -0.02em;
}
h6 {
  font-size: 20px;
  font-weight: normal;
  line-height: 1.3em;
}
@media screen and (max-width: 768px) {
  h1 {
    font-size: 40px;
    font-weight: normal;
    line-height: 1.3em;
    letter-spacing: -0.02em;
  }
  h2 {
    font-size: 36px;
    font-weight: normal;
    line-height: 1.3em;
    letter-spacing: -0.02em;
  }
}
p.has-x-large-font-size {
  font-size: 28px!important;
  font-weight: normal!important;
  line-height: 1.4em!important;
}
p.has-large-font-size {
  font-size: 21px!important;
  font-weight: normal!important;
  line-height: 1.4em!important;
}
p.has-medium-font-size {
  font-size: 18px!important;
  font-weight: normal!important;
  line-height: 1.4em!important;
}
html, body {
  font-size: 18px;
  font-weight: normal;
  line-height: 1.4em;
}
p.has-small-font-size {
  font-size: 14px!important;
  font-weight: normal!important;
  line-height: 1.4em!important;
}
@media screen and (max-width: 768px) {
  p.has-medium-font-size {
    font-size: 16px!important;
    font-weight: normal!important;
    line-height: 1.4em!important;
  }
  html, body {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4em;
  }
}
/*-----------------------------------------------------------*/
/* FONTS                                                     */
/*-----------------------------------------------------------*/
@font-face {
  font-family: "Poppins";
  src: url("/wp-content/uploads/2025/03/Poppins-Regular.ttf") format("truetype");
  font-weight: 400;
  /* Regular */
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("/wp-content/uploads/2025/03/Poppins-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
}
@font-face {
  font-family: "Poppins";
  src: url("/wp-content/uploads/2025/06/Poppins-Medium.ttf") format("truetype");
  font-weight: 500;
  /* Bold */
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("/wp-content/uploads/2025/06/Poppins-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
}
@font-face {
  font-family: "Poppins";
  src: url("/wp-content/uploads/2025/03/Poppins-Bold.ttf") format("truetype");
  font-weight: 700;
  /* Bold */
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("/wp-content/uploads/2025/03/Poppins-BoldItalic.ttf") format("truetype");
  font-weight: 700;
  font-style: italic;
}
/*-----------------------------------------------------------*/
/* GLOBAL                                                    */
/*-----------------------------------------------------------*/
html, body {
  font-family: "Poppins", sans-serif;
  letter-spacing: -0.005em;
  text-wrap: pretty;
}
@media screen and (max-width: 768px) {
  html, body {
    font-size: 16px;
  }
}
body {
  background: linear-gradient(215deg, #305280 0%, #1B2E48 25%);
  background-color: #1B2E48;
  background-repeat: no-repeat;
}
body.single-post {
  background: linear-gradient(190deg, #375d92 0%, #1B2E48 35%);
}
.site {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'><filter id='noise'><feTurbulence type='fractalNoise' baseFrequency='4' numOctaves='5' stitchTiles='stitch'/><feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0'/></filter><rect width='100%' height='100%' filter='url(%23noise)' opacity='.75'/></svg>");
  background-size: 550px 550px;
}
h1, h2, h3, h4, h5, h6 {
  font-family: "Poppins", sans-serif;
  margin: 0;
  font-weight: normal;
  text-wrap: pretty;
}
h1.medium, h2.medium, h3.medium, h4.medium, h5.medium, h6.medium {
  font-weight: 500 !important;
}
h1.medium-strong strong, h2.medium-strong strong, h3.medium-strong strong, h4.medium-strong strong, h5.medium-strong strong, h6.medium-strong strong {
  font-weight: 500 !important;
}
h1 {
  font-weight: normal !important;
}
h2 {
  font-weight: normal !important;
}
h3 {
  font-weight: normal !important;
}
h4 {
  font-weight: normal !important;
}
h5 {
  font-weight: normal !important;
}
h6 {
  font-weight: normal !important;
}
/*-----------------------------------------------------------*/
/* GLOBAL CLASSES                                            */
/*-----------------------------------------------------------*/
.none {
  display: none !important;
}
.site-main {
  margin: 0 !important;
}
.site-main .inside-article {
  padding: 0 !important;
}
p {
  margin: 0;
  line-height: 1.5em;
}
.site {
  width: 100vw;
  max-width: unset;
}
.no-shadow {
  box-shadow: none !important;
}
.yellow-strong strong {
  color: #f9dc05;
}
/*-----------------------------------------------------------*/
/* LAYOUT                                                    */
/*-----------------------------------------------------------*/
div#page {
  padding-top: 100px !important;
}
@media screen and (max-width: 768px) {
  div#page {
    padding-top: 80px !important;
  }
}
.wrapper__inner {
  padding-left: 40px;
  padding-right: 40px;
}
@media screen and (max-width: 1024px) {
  .wrapper__inner {
    padding-left: 24px;
    padding-right: 24px;
  }
}
/*-----------------------------------------------------------*/
/* SCROLLBAR                                                 */
/*-----------------------------------------------------------*/
::-webkit-scrollbar {
  width: 8px;
  scrollbar-width: none;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}
::-webkit-scrollbar-track {
  background-color: #495f7e;
}
::-webkit-scrollbar-thumb {
  background-color: #122035;
}
/*-----------------------------------------------------------*/
/* ADMIN EDITOR                                              */
/*-----------------------------------------------------------*/
.editor-visual-editor {
  background-color: #1B2E48;
}
/*-----------------------------------------------------------*/
/* ARCHIVE LAYOUT                                            */
/*-----------------------------------------------------------*/
.archive header.page-header {
  display: none !important;
}



.cmplz-hidden {
					display: none !important;
				}

.perfmatters-lazy[data-src]{display:none !important;}

.perfmatters-lazy-youtube{position:relative;width:100%;max-width:100%;height:0;padding-bottom:56.23%;overflow:hidden}.perfmatters-lazy-youtube img{position:absolute;top:0;right:0;bottom:0;left:0;display:block;width:100%;max-width:100%;height:auto;margin:auto;border:none;cursor:pointer;transition:.5s all;-webkit-transition:.5s all;-moz-transition:.5s all}.perfmatters-lazy-youtube img:hover{-webkit-filter:brightness(75%)}.perfmatters-lazy-youtube .play{position:absolute;top:50%;left:50%;right:auto;width:68px;height:48px;margin-left:-34px;margin-top:-24px;background:url(https://ml8kekmuqldl.i.optimole.com/w:auto/h:auto/q:mauto/f:best/ig:avif/https://www.davidcalik.sk/wp-content/plugins/perfmatters/img/youtube.svg) no-repeat;background-position:center;background-size:cover;pointer-events:none;filter:grayscale(1)}.perfmatters-lazy-youtube:hover .play{filter:grayscale(0)}.perfmatters-lazy-youtube iframe{position:absolute;top:0;left:0;width:100%;height:100%;z-index:99}.wp-has-aspect-ratio .wp-block-embed__wrapper{position:relative;}.wp-has-aspect-ratio .perfmatters-lazy-youtube{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;height:100%;padding-bottom:0}


       .gallery-bottom-button-wrapper {
            display: flex;
            text-align: left;
            margin-top: 15px;
            padding-left: 10px;
            justify-content: space-between;
            align-content: center;
            align-items: center;
        }

        .gallery-photo-count {
            font-size: 13px;
            color: #666;
            text-align: right;
            margin-top: 5px;
            padding-right: 10px;
        }

        .gallery-button-static {
            background-color: rgba(0, 0, 0, 0.3);
            color: white;
            padding: 10px 40px;
            border-radius: 100px;
            text-decoration: none;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: background 0.3s;
            font-size: 14px;
            min-width: 180px;
            display: inline-block;
            text-align: center;
            white-space: nowrap;
        }

        .gallery-button-static:hover {
            background-color: rgba(0, 0, 0, 0.9);
        }

        .thumb-slide img {
            width: 100%;
            height: auto;
             aspect-ratio: 16/12;
            object-fit: cover;
        }

        #nav-slider {
            margin: 10px auto;
        }

        #nav-slider .slick-slide {
            cursor: pointer;
            margin: 0 5px;
        }

        #nav-slider .slick-prev,
        #nav-slider .slick-next {
            display: none !important;
        }
        
    

.gb-element-a29cc9d9{align-items:center;column-gap:8px;display:flex;flex-direction:row;flex-wrap:wrap;justify-content:flex-start;row-gap:8px}

.gb-element-a29cc9d9{align-items:center;column-gap:8px;display:flex;flex-direction:row;flex-wrap:wrap;justify-content:flex-start;row-gap:8px}

form.fluent_form_3 .ff-btn-submit:not(.ff_btn_no_style) { background-color: var(--fluentform-primary); color: #ffffff; }


.wp-elements-d9e2a507141fa1df1dfd94bed5d9c11e a:where(:not(.wp-element-button)){color:#868f92;}.wp-elements-dbc4e942787f18d3a8ffdd2640205384 a:where(:not(.wp-element-button)){color:#868f92;}
