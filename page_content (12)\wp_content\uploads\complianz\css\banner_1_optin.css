input[type="checkbox"].cmplz-category { width: initial; -webkit-appearance: checkbox;}input[type="checkbox"].cmplz-category:checked::before { content: '';}.cmplz-cookiebanner { box-sizing: border-box;}:root { --cmplz_banner_width: 526px; --cmplz_banner_background_color: #ffffff; --cmplz_banner_border_color: #f2f2f2; --cmplz_banner_border_width: 0px 0px 0px 0px ; --cmplz_banner_border_radius: 12px 12px 12px 12px; --cmplz_banner_margin: 10px; --cmplz_categories-height: 163px; --cmplz_title_font_size: 15px; --cmplz_text_line_height: calc(var(--cmplz_text_font_size) * 1.5); --cmplz_text_color: #222222; --cmplz_hyperlink_color: #3f6394; --cmplz_text_font_size: 12px; --cmplz_link_font_size: 12px; --cmplz_category_body_font_size: 12px; --cmplz_button_accept_background_color: #3f6394; --cmplz_button_accept_border_color: #3f6394; --cmplz_button_accept_text_color: #ffffff; --cmplz_button_deny_background_color: #dfdfdf; --cmplz_button_deny_border_color: #dfdfdf; --cmplz_button_deny_text_color: #a7a7a7; --cmplz_button_settings_background_color: #dfdfdf; --cmplz_button_settings_border_color: #dfdfdf; --cmplz_button_settings_text_color: #a7a7a7; --cmplz_button_border_radius: 6px 6px 6px 6px; --cmplz_button_font_size: 15px; --cmplz_category_header_always_active_color: green; --cmplz_category_header_title_font_size: 14px; --cmplz_category_header_active_font_size: 12px; --cmplz-manage-consent-height: 50px; --cmplz-manage-consent-offset: -35px; --cmplz_slider_active_color: #3f6394; --cmplz_slider_inactive_color: #F56E28; --cmplz_slider_bullet_color: #ffffff;}#cmplz-manage-consent .cmplz-manage-consent { margin: unset; z-index: 9998; color: var(--cmplz_text_color); background-color: var(--cmplz_banner_background_color); border-style: solid; border-color: var(--cmplz_banner_border_color); border-width: var(--cmplz_banner_border_width); border-radius: var(--cmplz_banner_border_radius); border-bottom-left-radius: 0; border-bottom-right-radius: 0; line-height: initial; position: fixed; bottom: var(--cmplz-manage-consent-offset); min-width: 100px; height: var(--cmplz-manage-consent-height); right: 40px; padding: 15px; cursor: pointer; animation: mc_slideOut 0.5s forwards;}#cmplz-manage-consent .cmplz-manage-consent:active { outline: none; border: none;}#cmplz-manage-consent .cmplz-manage-consent.cmplz-dismissed { display: none;}#cmplz-manage-consent .cmplz-manage-consent:hover,#cmplz-manage-consent .cmplz-manage-consent:focus { animation: mc_slideIn 0.5s forwards; animation-delay: 0;}@-webkit-keyframes mc_slideIn { 100% { bottom: 0; }}@keyframes mc_slideIn { 100% { bottom: 0; }}@-webkit-keyframes mc_slideOut { 100% { bottom: var(--cmplz-manage-consent-offset); }}@keyframes mc_slideOut { 100% { bottom: var(--cmplz-manage-consent-offset); }}@media (max-width: 425px) { .cmplz-cookiebanner .cmplz-header .cmplz-title { display: none; }}.cmplz-cookiebanner { max-height: calc(100vh - 20px); position: fixed; height: auto; left: 50%; top: 50%; -ms-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); grid-template-rows: minmax(0, 1fr); z-index: 99999; background: var(--cmplz_banner_background_color); border-style: solid; border-color: var(--cmplz_banner_border_color); border-width: var(--cmplz_banner_border_width); border-radius: var(--cmplz_banner_border_radius); padding: 15px 20px; display: grid; grid-gap: 10px;}.cmplz-cookiebanner a { transition: initial;}.cmplz-cookiebanner .cmplz-buttons a.cmplz-btn.tcf { display: none;}.cmplz-cookiebanner.cmplz-dismissed { display: none;}.cmplz-cookiebanner .cmplz-body { width: 100%; grid-column: span 3; overflow-y: auto; overflow-x: hidden; max-height: 55vh;}.cmplz-cookiebanner .cmplz-body::-webkit-scrollbar-track { border-radius: 10px; -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0); background-color: transparent;}.cmplz-cookiebanner .cmplz-body::-webkit-scrollbar { width: 5px; background-color: transparent;}.cmplz-cookiebanner .cmplz-body::-webkit-scrollbar-thumb { background-color: var(--cmplz_button_accept_background_color); border-radius: 10px;}@media (min-width: 350px) { .cmplz-cookiebanner .cmplz-body { min-width: 300px; }}.cmplz-cookiebanner .cmplz-divider { margin-left: -20px; margin-right: -20px;}.cmplz-cookiebanner .cmplz-header { grid-template-columns: 100px 1fr 100px; align-items: center; display: grid; grid-column: span 3;}.cmplz-cookiebanner .cmplz-logo svg { max-height: 35px; width: inherit;}.cmplz-cookiebanner .cmplz-logo img { max-height: 40px; width: inherit;}.cmplz-cookiebanner .cmplz-title { justify-self: center; grid-column-start: 2; font-size: var(--cmplz_title_font_size); color: var(--cmplz_text_color); font-weight: 500;}.cmplz-cookiebanner .cmplz-close { line-height: 20px; justify-self: end; grid-column-start: 3; font-size: 20px; cursor: pointer; width: 20px; height: 20px; color: var(--cmplz_text_color);}.cmplz-cookiebanner .cmplz-close svg { width: 20px; height: 20px;}.cmplz-cookiebanner .cmplz-close:hover { text-decoration: none; line-height: initial; font-size: 18px;}.cmplz-cookiebanner .cmplz-message { word-wrap: break-word; font-size: var(--cmplz_text_font_size); line-height: var(--cmplz_text_line_height); color: var(--cmplz_text_color); margin-bottom: 5px;}.cmplz-cookiebanner .cmplz-message a { color: var(--cmplz_hyperlink_color);}.cmplz-cookiebanner .cmplz-message,.cmplz-cookiebanner .cmplz-categories,.cmplz-cookiebanner .cmplz-links,.cmplz-cookiebanner .cmplz-buttons,.cmplz-cookiebanner .cmplz-divider { grid-column: span 3;}.cmplz-cookiebanner .cmplz-categories.cmplz-tcf .cmplz-category .cmplz-category-header { grid-template-columns: 1fr auto;}.cmplz-cookiebanner .cmplz-categories .cmplz-category { background-color: rgba(239, 239, 239, 0.5);}.cmplz-cookiebanner .cmplz-categories .cmplz-category:not(:last-child) { margin-bottom: 10px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header { display: grid; grid-template-columns: 1fr auto 15px; grid-template-rows: minmax(0, 1fr); align-items: center; grid-gap: 10px; padding: 10px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-category-title { font-weight: 500; grid-column-start: 1; justify-self: start; font-size: var(--cmplz_category_header_title_font_size); color: var(--cmplz_text_color); margin: 0;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-always-active { font-size: var(--cmplz_category_header_active_font_size); font-weight: 500; color: var(--cmplz_category_header_always_active_color);}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-always-active label { display: none;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-banner-checkbox { display: flex; align-items: center; margin: 0;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-banner-checkbox input[data-category=cmplz_functional] { display: none;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-icon.cmplz-open { grid-column-start: 3; cursor: pointer; content: ''; transform: rotate(0deg); -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; background-size: cover; height: 18px; width: 18px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category[open] .cmplz-icon.cmplz-open { transform: rotate(180deg);}.cmplz-cookiebanner .cmplz-categories .cmplz-category summary { display: block;}.cmplz-cookiebanner .cmplz-categories .cmplz-category summary::marker { display: none; content: '';}.cmplz-cookiebanner .cmplz-categories .cmplz-category summary::-webkit-details-marker { display: none; content: '';}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-description { font-size: var(--cmplz_category_body_font_size); color: var(--cmplz_text_color); margin: 0; max-width: 100%; padding: 10px;}@media (max-width: 425px) { .cmplz-cookiebanner .cmplz-category .cmplz-category-header { grid-template-columns: 1fr!important; }}@media (max-width: 425px) { .cmplz-cookiebanner .cmplz-message { margin-right: 5px; }}.cmplz-cookiebanner .cmplz-buttons { display: flex; gap: var(--cmplz_banner_margin);}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn { height: 45px; padding: 10px; margin: initial; width: 100%; white-space: nowrap; border-radius: var(--cmplz_button_border_radius); cursor: pointer; font-size: var(--cmplz_button_font_size); font-weight: 500; text-decoration: none; line-height: 20px; text-align: center; flex: initial;}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn:hover { text-decoration: none;}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-accept { background-color: var(--cmplz_button_accept_background_color); border: 1px solid var(--cmplz_button_accept_border_color); color: var(--cmplz_button_accept_text_color);}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-deny { background-color: var(--cmplz_button_deny_background_color); border: 1px solid var(--cmplz_button_deny_border_color); color: var(--cmplz_button_deny_text_color);}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-view-preferences { background-color: var(--cmplz_button_settings_background_color); border: 1px solid var(--cmplz_button_settings_border_color); color: var(--cmplz_button_settings_text_color);}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-save-preferences { background-color: var(--cmplz_button_settings_background_color); border: 1px solid var(--cmplz_button_settings_border_color); color: var(--cmplz_button_settings_text_color);}.cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-manage-options { background-color: var(--cmplz_button_settings_background_color); border: 1px solid var(--cmplz_button_settings_border_color); color: var(--cmplz_button_settings_text_color);}.cmplz-cookiebanner .cmplz-buttons a.cmplz-btn { display: flex; align-items: center; justify-content: center; padding: initial;}@media (max-width: 425px) { .cmplz-cookiebanner .cmplz-links.cmplz-information { display: initial; text-align: center; }}.cmplz-cookiebanner .cmplz-links { display: flex; gap: var(--cmplz_banner_margin);}.cmplz-cookiebanner .cmplz-links.cmplz-information { justify-content: space-between;}.cmplz-cookiebanner .cmplz-links.cmplz-documents { justify-content: center;}.cmplz-cookiebanner .cmplz-links .cmplz-link { color: var(--cmplz_hyperlink_color); font-size: var(--cmplz_link_font_size); text-decoration: underline; margin: 0;}.cmplz-cookiebanner .cmplz-links .cmplz-link.cmplz-read-more-purposes { display: none;}.cmplz-categories .cmplz-category .cmplz-description-statistics-anonymous { display: none;}.cmplz-categories .cmplz-category .cmplz-description-statistics { display: block;}.cmplz-cookiebanner { top: initial; left: 10px; bottom: 10px; transform: initial;}@media (min-width: 768px) { .cmplz-cookiebanner { min-width: var(--cmplz_banner_width); max-width: 100%; display: grid; grid-row-gap: 10px; }}@media (max-width: 768px) { .cmplz-cookiebanner { left: initial; right: initial; bottom: 0; width: 100%; } .cmplz-cookiebanner .cmplz-buttons { flex-direction: column; }}@media (min-width: 768px) { .cmplz-message, .cmplz-categories { width: calc(var(--cmplz_banner_width) - 42px); }}#cmplz-manage-consent .cmplz-manage-consent { left: 40px; right: initial;}.cmplz-cookiebanner .cmplz-categories,.cmplz-cookiebanner .cmplz-save-preferences,.cmplz-cookiebanner .cmplz-link.cmplz-manage-options,.cmplz-cookiebanner .cmplz-manage-vendors,.cmplz-cookiebanner .cmplz-read-more,.cmplz-cookiebanner .cmplz-btn.cmplz-manage-options { display: none;}.cmplz-cookiebanner .cmplz-categories.cmplz-fade-in { animation: fadeIn 1s; -webkit-animation: fadeIn 1s; -moz-animation: fadeIn 1s; -o-animation: fadeIn 1s; -ms-animation: fadeIn 1s; display: block;}@keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; }}@-moz-keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; }}@-webkit-keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; }}@-o-keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; }}@-ms-keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; }}.cmplz-cookiebanner .cmplz-links.cmplz-information { display: none;}.cmplz-cookiebanner .cmplz-preferences,.cmplz-manage-consent-container .cmplz-preferences { display: none;}@media (max-width: 768px) { #cmplz-manage-consent .cmplz-manage-consent { display: none; }}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox { position: relative;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox input.cmplz-consent-checkbox { opacity: 0; margin: 0; margin-top: -10px; cursor: pointer; -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"; filter: alpha(opacity=0); -moz-opacity: 0; -khtml-opacity: 0; position: absolute; z-index: 1; top: 0px; left: 0px; width: 40px; height: 20px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox input.cmplz-consent-checkbox:focus + .cmplz-label:before { box-shadow: 0 0 0 2px #245fcc;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox input.cmplz-consent-checkbox:checked + .cmplz-label::before { display: block; background-color: var(--cmplz_slider_active_color); content: ""; padding-left: 6px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox input.cmplz-consent-checkbox:checked + .cmplz-label:after { left: 14px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox .cmplz-label { position: relative; padding-left: 30px; margin: 0;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox .cmplz-label:before,.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox .cmplz-label:after { box-sizing: border-box; position: absolute; -webkit-border-radius: 10px; -moz-border-radius: 10px; border-radius: 10px; transition: background-color 0.3s, left 0.3s;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox .cmplz-label:before { display: block; content: ""; color: #fff; box-sizing: border-box; font-family: 'FontAwesome', sans-serif; padding-left: 23px; font-size: 12px; line-height: 20px; background-color: var(--cmplz_slider_inactive_color); left: 0px; top: -7px; height: 15px; width: 28px; -webkit-border-radius: 10px; -moz-border-radius: 10px; border-radius: 10px;}.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-banner-checkbox .cmplz-label:after { display: block; content: ""; letter-spacing: 20px; background: var(--cmplz_slider_bullet_color); left: 4px; top: -5px; height: 11px; width: 11px;}.cmplz-cookiebanner { box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;}#cmplz-manage-consent .cmplz-manage-consent { box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;}.cmplz-cookiebanner.cmplz-show { box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;}.cmplz-cookiebanner.cmplz-show:hover { transition-duration: 1s; box-shadow: rgba(0, 0, 0, 0.3) 0px 19px 38px, rgba(0, 0, 0, 0.22) 0px 15px 12px;}.cmplz-categories .cmplz-category .cmplz-description-statistics-anonymous { display: block;}.cmplz-categories .cmplz-category .cmplz-description-statistics { display: none;} .cmplz-placeholder-element > blockquote.fb-xfbml-parse-ignore { margin: 0 20px; } .instagram-media.cmplz-placeholder-element > div { max-width: 100%; }