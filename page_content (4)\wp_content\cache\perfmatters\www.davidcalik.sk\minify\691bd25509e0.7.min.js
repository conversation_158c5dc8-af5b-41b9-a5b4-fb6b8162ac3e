document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(".jet-list-tree__parent input").forEach(parentInput=>{parentInput.addEventListener("change",function(){let parent=this.closest(".jet-list-tree__parent");let childrenContainer=parent.nextElementSibling;if(!childrenContainer||!childrenContainer.classList.contains("jet-list-tree__children"))return;if(this.checked&&childrenContainer.querySelector("input:checked")){this.checked=!1;return}
if(this.checked){parent.classList.add("open");childrenContainer.classList.add("open");let label=this.getAttribute("data-label");childrenContainer.setAttribute("data-label",label)}else{if(!childrenContainer.querySelector("input:checked")){parent.classList.remove("open");childrenContainer.classList.remove("open");childrenContainer.removeAttribute("data-label")}}})});document.querySelectorAll(".jet-list-tree__children input").forEach(childInput=>{childInput.addEventListener("change",function(){let childrenContainer=this.closest(".jet-list-tree__children");let parent=childrenContainer?childrenContainer.previousElementSibling:null;let parentInput=parent?parent.querySelector("input"):null;if(!parent||!parentInput)return;if(this.checked){parentInput.checked=!1;parent.classList.add("open");childrenContainer.classList.add("open");let label=parentInput.getAttribute("data-label");childrenContainer.setAttribute("data-label",label)}
if(!childrenContainer.querySelector("input:checked")&&!parentInput.checked){parent.classList.remove("open");childrenContainer.classList.remove("open");childrenContainer.removeAttribute("data-label")}})});document.querySelectorAll(".jet-list-tree__parent input:checked").forEach(parentInput=>{let parent=parentInput.closest(".jet-list-tree__parent");let childrenContainer=parent.nextElementSibling;if(!childrenContainer||!childrenContainer.classList.contains("jet-list-tree__children"))return;if(childrenContainer.querySelector("input:checked")){parentInput.checked=!1;return}
parent.classList.add("open");childrenContainer.classList.add("open");let label=parentInput.getAttribute("data-label");if(label){childrenContainer.setAttribute("data-label",label)}});document.querySelectorAll(".jet-list-tree__children input:checked").forEach(childInput=>{let childrenContainer=childInput.closest(".jet-list-tree__children");let parent=childrenContainer?childrenContainer.previousElementSibling:null;let parentInput=parent?parent.querySelector("input"):null;if(!parent||!parentInput)return;parentInput.checked=!1;parent.classList.add("open");childrenContainer.classList.add("open");let label=parentInput.getAttribute("data-label");if(label){childrenContainer.setAttribute("data-label",label)}});const clearFiltersBtn=document.querySelector(".jet-remove-all-filters__button");if(clearFiltersBtn){clearFiltersBtn.addEventListener("click",()=>{document.querySelectorAll(".jet-list-tree__parent input, .jet-list-tree__children input").forEach(input=>{input.checked=!1});document.querySelectorAll(".jet-list-tree__parent, .jet-list-tree__children").forEach(el=>{el.classList.remove("open")});document.querySelectorAll(".jet-list-tree__children").forEach(el=>{el.removeAttribute("data-label")})})}})